import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/flashcard_set.dart';
import '../providers/flashcard_provider.dart';

class SetEditScreen extends StatefulWidget {
  final FlashcardSet? flashcardSet;

  const SetEditScreen({super.key, this.flashcardSet});

  @override
  State<SetEditScreen> createState() => _SetEditScreenState();
}

class _SetEditScreenState extends State<SetEditScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.flashcardSet != null) {
      _nameController.text = widget.flashcardSet!.name;
      _descriptionController.text = widget.flashcardSet!.description;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.flashcardSet != null;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'Edit Set' : 'Create New Set'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveSet,
            child: Text(
              'Save',
              style: TextStyle(
                color: _isLoading 
                    ? Theme.of(context).disabledColor 
                    : Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Set Information',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Set Name',
                  hintText: 'Enter a name for your flashcard set',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.title),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a name for the set';
                  }
                  if (value.trim().length < 2) {
                    return 'Set name must be at least 2 characters long';
                  }
                  return null;
                },
                textInputAction: TextInputAction.next,
                enabled: !_isLoading,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  hintText: 'Describe what this set is about',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.description),
                ),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a description for the set';
                  }
                  if (value.trim().length < 5) {
                    return 'Description must be at least 5 characters long';
                  }
                  return null;
                },
                textInputAction: TextInputAction.done,
                enabled: !_isLoading,
              ),
              const SizedBox(height: 24),
              if (_isLoading)
                const Center(
                  child: CircularProgressIndicator(),
                ),
              const Spacer(),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _saveSet,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: Text(
                    isEditing ? 'Update Set' : 'Create Set',
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveSet() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final provider = context.read<FlashcardProvider>();
    bool success;

    try {
      if (widget.flashcardSet != null) {
        // Update existing set
        final updatedSet = widget.flashcardSet!.copyWith(
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim(),
        );
        success = await provider.updateFlashcardSet(updatedSet);
      } else {
        // Create new set
        success = await provider.createFlashcardSet(
          _nameController.text.trim(),
          _descriptionController.text.trim(),
        );
      }

      if (success && mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.flashcardSet != null 
                  ? 'Set updated successfully!' 
                  : 'Set created successfully!',
            ),
            backgroundColor: Colors.green,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              provider.error ?? 'Failed to save set. Please try again.',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('An error occurred: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

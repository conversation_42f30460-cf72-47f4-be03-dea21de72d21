import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/flashcard_provider.dart';
import '../models/flashcard_set.dart';
import '../widgets/flashcard_set_card.dart';
import 'set_edit_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // Load flashcard sets when the screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<FlashcardProvider>().loadFlashcardSets();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Flashcard App'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Consumer<FlashcardProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (provider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error: ${provider.error}',
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => provider.loadFlashcardSets(),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (provider.flashcardSets.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.style_outlined,
                    size: 64,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No flashcard sets yet',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Create your first flashcard set to get started!',
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () => _navigateToCreateSet(context),
                    icon: const Icon(Icons.add),
                    label: const Text('Create Set'),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () => provider.loadFlashcardSets(),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Your Flashcard Sets',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: ListView.builder(
                      itemCount: provider.flashcardSets.length,
                      itemBuilder: (context, index) {
                        final set = provider.flashcardSets[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 12.0),
                          child: FlashcardSetCard(
                            flashcardSet: set,
                            onTap: () => _navigateToFlashcardList(context, set),
                            onEdit: () => _navigateToEditSet(context, set),
                            onDelete: () => _showDeleteDialog(context, set),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToCreateSet(context),
        tooltip: 'Create New Set',
        child: const Icon(Icons.add),
      ),
    );
  }

  void _navigateToFlashcardList(BuildContext context, FlashcardSet set) {
    Navigator.pushNamed(
      context,
      '/flashcard-list',
      arguments: set,
    );
  }

  void _navigateToCreateSet(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SetEditScreen(),
      ),
    );
  }

  void _navigateToEditSet(BuildContext context, FlashcardSet set) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SetEditScreen(flashcardSet: set),
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, FlashcardSet set) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Set'),
          content: Text(
            'Are you sure you want to delete "${set.name}"? This will also delete all flashcards in this set.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                context.read<FlashcardProvider>().deleteFlashcardSet(set.id!);
              },
              style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.error,
              ),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );
  }
}
